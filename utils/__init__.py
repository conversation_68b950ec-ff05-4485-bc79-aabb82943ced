import logging
import mimetypes
import re
import secrets
from typing import Union

import requests
from django.core.files import File
from django.core.files.base import ContentFile
from django.core.cache import cache
from drf_yasg import openapi
from rest_framework import serializers

from utils.ytdl import YoutubeDownloader
from utils.ytdl.oneapi import <PERSON><PERSON><PERSON>


def absolute_url(req, url):
    """
        can either be a file instance or a URL string
    """
    try:
        return req.build_absolute_uri(url.url if hasattr(url, 'url') else url)
    except Exception:
        return None


def sizeof_fmt(num, suffix="B"):
    for unit in ["", "K", "M", "G"]:
        if abs(num) < 1024.0:
            return f"{num:3.1f} {unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f} Yi{suffix}"


class TranslationJSONField(serializers.JSONField):
    class Meta:
        swagger_schema_fields = {
            "type": openapi.TYPE_OBJECT,
            "required": ["title", "language_code"],
            "properties": {
                "title": {
                    "title": "title",
                    "type": openapi.TYPE_STRING,
                },
                "language_code": {
                    "title": "language code",
                    "description": "eg. en, da",
                    "type": openapi.TYPE_STRING,
                },
            }
        }


def extract_id_of_youtube_url(video_url) -> Union[str, bool]:
    if "youtu.be" not in video_url and "youtube.com" not in video_url:
        return False

    youtube_id_length = 11
    if "youtu.be" in video_url:
        start_pint = video_url.rfind('.be/') + 4
        video_id = video_url[start_pint:start_pint + youtube_id_length]
    else:
        start_pint = video_url.rfind('?v=') + 3
        video_id = video_url[start_pint: start_pint + youtube_id_length]

    return video_id


# def download_youtube_audio(link):
#     instance = OneApi(link)
#     des = instance.download_audio()
#     return des

def download_youtube_audio(link):
    yt = OneApi(link)
    file_instance = yt.download_audio()
    return file_instance

def list_display_thumbnail(image):
    from django.utils.safestring import mark_safe
    try:
        return mark_safe(
            f"<img style='width:70px;object-fit: contain;border-radius:7px' src={image.url}>"
        )
    except Exception:
        return '-'


def get_uploaded_filepath(url):
    from django.conf import settings

    if not url:
        return ''
    import urllib

    file_path = urllib.parse.unquote(url)
    file_path = file_path[file_path.find('tmp/'):]
    if settings.DEBUG:
        file_path = "static/" + file_path
    else:
        file_path = "staticfiles/" + file_path

    return file_path


def generate_slug_for_model(model, value: str, recycled_count: int = 0):
    from slugify import slugify

    slug = slugify(value)
    if model.objects.filter(slug=slug).exists():
        recycled_count += 1
        if value.endswith(f'-{recycled_count - 1}'):
            value = value.replace(f'-{recycled_count - 1}', f'-{recycled_count}')
        else:
            value = f"{value}-{recycled_count}"
        return generate_slug_for_model(model, value, recycled_count)

    return slug[:50]


def file_location(path):
    from django.conf import settings

    if settings.DEBUG:
        return "static" + path
    else:
        return "staticfiles" + path


def guess_file_type(filename):
    try:
        mimetype = mimetypes.guess_type(filename)[0].split('/')[0]
        return mimetype

    except Exception:
        return False


def sizeof_fmt(num, suffix="B"):
    for unit in ["", "K", "M", "G"]:
        if abs(num) < 1024.0:
            return f"{num:3.1f} {unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f} Yi{suffix}"


def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_translation_schema():
    from dj_language.models import Language
    from django.utils.translation import gettext_lazy as _
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }
def get_description_translation_schema():
    from dj_language.models import Language
    from django.utils.translation import gettext_lazy as _
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', "format": "textarea", 'title': str(_('Description'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }

def get_country_by_ip(client_ip):
    try:
        from django.contrib.gis.geoip2 import GeoIP2

        g = GeoIP2()
        return {
            'country_name': g.country_name(client_ip),
            'country_code': g.country_code(client_ip),
        }

    except Exception as e:
        logging.error(f"error getting ip country: {e} ip: {client_ip}")
        return {}


def remote_file_to_django_file(url):
    try:
        req = requests.get(url, allow_redirects=True)
        if len(req.content):
            if content_disposition := req.headers.get('Content-Disposition'):
                filename = re.search(r'filename="([^"]+)"', content_disposition).groups()[0]
            else:
                ext = '.' + req.headers.get('Content-Type').split('/')[1]
                filename = secrets.token_urlsafe(4) + ext

            return File(ContentFile(req.content), name=filename)

    except Exception as e:
        print(e)

    return None


def get_language_safely(language_code, fallback='en'):
    """
    Get Language object with fallback support and proper logging
    """
    from dj_language.models import Language

    try:
        language = Language.objects.get(code=language_code)
        logging.info(f"Language found: {language_code} -> {language.name}")
        return language
    except Language.DoesNotExist:
        logging.warning(f"Language not found: {language_code}, trying fallback: {fallback}")

        try:
            fallback_language = Language.objects.get(code=fallback)
            logging.info(f"Fallback language used: {fallback} -> {fallback_language.name}")
            return fallback_language
        except Language.DoesNotExist:
            # This is a critical error - no fallback language available
            critical_error_msg = f"CRITICAL: Fallback language also not found: {fallback}. Database may be corrupted!"
            logging.error(critical_error_msg)
            # Critical error logged

            # Try to get any language as last resort
            try:
                any_language = Language.objects.first() 
                if any_language:
                    logging.warning(f"Using any available language: {any_language.code}")
                    return any_language
                else:
                    empty_db_error = "CRITICAL: No languages found in database at all!"
                    logging.critical(empty_db_error)

                    from types import SimpleNamespace
                    return SimpleNamespace(code=language_code or 'en', name='Unknown')

            except Exception as e:
                # Database connection issue
                db_error_msg = f"Database error while fetching languages: {e}"
                logging.critical(db_error_msg)
                from types import SimpleNamespace
                return SimpleNamespace(code=language_code or 'en', name='Unknown')


def get_language_aware_cache_key(base_key, language_code):
    """
    Generate cache key that includes normalized language code
    """
    normalized_lang = language_code.split('-')[0].lower() if language_code else 'en'
    return f"{base_key}_{normalized_lang}"
