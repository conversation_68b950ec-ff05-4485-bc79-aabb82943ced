import json
import os
import re
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings

from apps.hadis.models import Hadis, Category, Narrates, Reference, ReferenceAddr


class Command(BaseCommand):
    help = 'Import <PERSON>j al-Ba<PERSON>ha letters from JSON file'
    
    # Default metadata for all hadises
    DEFAULT_METADATA = {
        "age": {
            "to_13": False,
            "for_all": True,
            "from_40": False,
            "from_13_18": False,
            "from_18_28": False,
            "from_28_40": False
        },
        "gender": {
            "male": False,
            "female": False,
            "for_all": True
        },
        "marital": {
            "for_all": True,
            "married_no_kids": False,
            "single_to_marry": False,
            "married_with_kids": False,
            "single_not_to_marry": False
        }
    }

    def add_arguments(self, parser):
        parser.add_argument(
            '--test',
            action='store_true',
            help='Run in test mode (no database changes)',
        )
        parser.add_argument(
            '--file',
            type=str,
            default='apps/hadis/data/nahj_letters.json',
            help='Path to JSON file',
        )

    def handle(self, *args, **options):
        self.test_mode = options['test']
        file_path = options['file']
        
        if self.test_mode:
            self.stdout.write(
                self.style.WARNING('Running in TEST MODE - no database changes will be made')
            )
        
        # Check if file exists
        if not os.path.exists(file_path):
            raise CommandError(f'File {file_path} does not exist')
        
        # Load JSON data
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            raise CommandError(f'Error loading JSON file: {e}')
        
        # Validate required constants
        try:
            self.main_category = Category.objects.get(id=15882)
            self.narrator = Narrates.objects.get(id=2)
            self.reference_book = Reference.objects.get(id=14)
        except Category.DoesNotExist:
            raise CommandError('Main category with ID 15882 not found')
        except Narrates.DoesNotExist:
            raise CommandError('Narrator with ID 2 not found')
        except Reference.DoesNotExist:
            raise CommandError('Reference book with ID 14 not found')
        
        self.stdout.write(f'Found {len(data["letters"])} letters to process')
        
        # Process data
        if self.test_mode:
            self._process_test_mode(data)
        else:
            self._process_real_mode(data)

    def _process_test_mode(self, data):
        """Process in test mode - no database changes"""
        created_categories = []
        created_hadises = []
        
        for letter in data['letters']:
            try:
                # Validate letter title
                if not letter.get('title') or not letter['title'].strip():
                    raise ValueError('Letter title cannot be empty')
                
                # Extract letter number from title
                letter_number = self._extract_letter_number(letter['title'])
                
                # Clean category title
                cleaned_title = self._clean_category_title(letter['title'])
                
                # Create category (test)
                category_data = {
                    'name': [{'language_code': 'fa', 'title': cleaned_title}],
                    'parent_id': 15882
                }
                created_categories.append(category_data)
                self.stdout.write(f'[TEST] Would create category: {cleaned_title} (original: {letter["title"]})')
                
                # Process containers
                for container in letter['containers']:
                    # Validate container data
                    if not container.get('arabic-text', {}).get('main'):
                        raise ValueError('Container must have arabic-text.main')
                    if not container.get('translate-text', {}).get('main'):
                        raise ValueError('Container must have translate-text.main')
                    
                    hadis_data = {
                        'text': container['arabic-text']['main'],
                        'translations': [
                            {
                                'language_code': 'fa',
                                'text': container['translate-text']['main']
                            }
                        ],
                        'narrated_by_id': 2,
                        'narrates_series': 'امام علی (ع)',
                        'category': category_data,
                        'meta_data': self.DEFAULT_METADATA
                    }
                    created_hadises.append(hadis_data)
                    
                    # Reference data
                    ref_data = {
                        'book_id': 14,
                        'ref_hadis_num': str(letter_number),
                        'ref_text': f'{self.reference_book.title}:{letter_number}'
                    }
                    
                    self.stdout.write(
                        f'[TEST] Would create hadis with text: {container["arabic-text"]["main"][:50]}...'
                    )
                    self.stdout.write(
                        f'[TEST] Would create reference: {ref_data["ref_text"]}'
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing letter {letter.get("title", "unknown")}: {e}')
                )
                continue
        
        self.stdout.write(
            self.style.SUCCESS(
                f'[TEST] Would create {len(created_categories)} categories and {len(created_hadises)} hadises'
            )
        )

    def _process_real_mode(self, data):
        """Process with actual database changes"""
        created_categories = 0
        created_hadises = 0
        created_references = 0
        
        try:
            with transaction.atomic():
                for letter in data['letters']:
                    try:
                        # Validate letter title
                        if not letter.get('title') or not letter['title'].strip():
                            raise ValueError('Letter title cannot be empty')
                        
                        # Extract letter number from title
                        letter_number = self._extract_letter_number(letter['title'])
                        
                        # Clean category title
                        cleaned_title = self._clean_category_title(letter['title'])
                        
                        # Create or get category
                        category = self._get_or_create_category(letter['title'])
                        created_categories += 1
                        
                        self.stdout.write(f'Processing letter: {letter["title"]} -> Category: {cleaned_title}')
                        
                        # Process containers
                        for container in letter['containers']:
                            # Validate container data
                            if not container.get('arabic-text', {}).get('main'):
                                raise ValueError('Container must have arabic-text.main')
                            if not container.get('translate-text', {}).get('main'):
                                raise ValueError('Container must have translate-text.main')
                            
                            # Create hadis
                            hadis = self._create_hadis(container, category)
                            created_hadises += 1
                            
                            # Create reference
                            self._create_reference(hadis, letter_number)
                            created_references += 1
                            
                            self.stdout.write(f'  Created hadis: {hadis.text[:50]}...')
                            
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error processing letter {letter.get("title", "unknown")}: {e}')
                        )
                        raise  # Re-raise to trigger rollback
                        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Transaction failed and rolled back: {e}')
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_categories} categories, '
                f'{created_hadises} hadises, and {created_references} references'
            )
        )

    def _extract_letter_number(self, title):
        """Extract letter number from title"""
        match = re.search(r'نامه (\d+)', title)
        if match:
            return int(match.group(1))
        else:
            raise ValueError(f'Could not extract letter number from title: {title}')

    def _clean_category_title(self, title):
        """Remove 'نامه X -' or 'خطبه X -' prefix from title"""
        if not title:
            return title
        
        # Remove pattern like "نامه 1 -", "نامه 23 -", "خطبه 1 -", "خطبه 23 -", etc.
        cleaned_title = re.sub(r'^(نامه|خطبه)\s+\d+\s*-\s*', '', title.strip())
        return cleaned_title.strip()

    def _get_or_create_category(self, title):
        """Get or create category with given title"""
        # Validate title is not empty
        if not title or not title.strip():
            raise ValueError('Category title cannot be empty')
        
        # Clean the title by removing "نامه X -" prefix
        cleaned_title = self._clean_category_title(title)
        
        # Validate cleaned title is not empty
        if not cleaned_title:
            raise ValueError(f'Category title is empty after cleaning: {title}')
        
        # Check if category already exists
        existing_categories = Category.objects.filter(
            name__contains=[{'language_code': 'fa', 'title': cleaned_title}]
        )
        
        if existing_categories.exists():
            category = existing_categories.first()
            if not category:
                raise ValueError(f'Found existing category but it is None for title: {cleaned_title}')
            return category
        
        # Create new category
        category = Category.objects.create(
            name=[{'language_code': 'fa', 'title': cleaned_title}],
            parent=self.main_category
        )
        
        if not category:
            raise ValueError(f'Failed to create category for title: {cleaned_title}')
            
        return category

    def _create_hadis(self, container, category):
        """Create hadis from container data"""
        # Validate category is not None or empty
        if not category:
            raise ValueError('Category cannot be None or empty when creating hadis')
        
        # Get next hadis number
        last_hadis = Hadis.objects.order_by('-number').first()
        next_number = (last_hadis.number + 1) if last_hadis else 1
        
        hadis = Hadis.objects.create(
            number=next_number,
            text=container['arabic-text']['main'],
            translations=[
                {
                    'language_code': 'fa',
                    'text': container['translate-text']['main']
                }
            ],
            narrated_by=self.narrator,
            narrates_series='امام علی (ع)',
            category=category,
            meta_data=self.DEFAULT_METADATA
        )
        return hadis

    def _create_reference(self, hadis, letter_number):
        """Create reference for hadis"""
        ref_text = f'{self.reference_book.title}: {letter_number}'
        
        ReferenceAddr.objects.create(
            hadis=hadis,
            book=self.reference_book,
            ref_hadis_num=str(letter_number),
            ref_text=ref_text
        )
